<?php
/**
 * Enhanced Responsive Footer with Magic UI Integration
 * Optimized for all devices with eliminated redundancy
 */

// Get footer data from ContentModel (passed from controller)
$footerData = $footer ?? [];
if (empty($footerData)) {
    // Fallback if footer data not available
    require_once APP_DIR . '/models/ContentModel.php';
    $footerData = ContentModel::getFooterData();
}

// Extract data sections
$company = $footerData['company'] ?? [];
$navigation = $footerData['navigation'] ?? [];
$services = $footerData['services'] ?? [];
$contact = $footerData['contact'] ?? [];
$social = $footerData['social'] ?? [];
$legal = $footerData['legal'] ?? [];

?>
<!-- Enhanced Responsive Footer with Magic UI Interactive Grid Pattern -->
<footer class="relative bg-gradient-to-br from-gray-900 via-black to-gray-800 text-gray-300 overflow-hidden">

    <!-- Magic UI Interactive Grid Pattern Background - Optimized for Performance -->
    <div class="absolute inset-0 opacity-10 md:opacity-20">
        <div class="absolute inset-0 bg-grid-pattern animate-grid-move"></div>
    </div>

    <!-- Optimized CSS Styles -->
    <style>
        .bg-grid-pattern {
            background-image:
                linear-gradient(rgba(56, 189, 248, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(56, 189, 248, 0.1) 1px, transparent 1px);
            background-size: 40px 40px;
        }

        @keyframes grid-move {
            0% { transform: translate(0, 0); }
            100% { transform: translate(40px, 40px); }
        }

        .animate-grid-move {
            animation: grid-move 25s linear infinite;
        }

        @media (prefers-reduced-motion: reduce) {
            .animate-grid-move {
                animation: none;
            }
        }

        .footer-link {
            @apply group flex items-center text-gray-400 hover:text-primary-400 transition-all duration-300 text-sm py-2 hover:translate-x-1;
        }

        .footer-icon {
            @apply mr-3 text-xs opacity-60 group-hover:opacity-100 transition-opacity duration-300;
        }

        .footer-section-title {
            @apply text-sm font-semibold text-white uppercase tracking-wider mb-4 md:mb-6 flex items-center;
        }

        .social-link {
            @apply w-10 h-10 md:w-12 md:h-12 flex items-center justify-center bg-gray-800/50 hover:bg-primary-500/20 border border-gray-700 hover:border-primary-500/50 rounded-xl text-gray-400 hover:text-primary-400 transition-all duration-300 hover:scale-110 hover:shadow-lg;
        }
    </style>

    <div class="relative">
        <!-- Main Footer Content -->
        <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-12 md:py-16">
            <!-- Enhanced Responsive Grid: 1 col (mobile), 2 cols (sm), 3 cols (md), 4 cols (lg+) -->
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8 md:gap-10 lg:gap-12">

                <!-- Company Brand Section -->
                <div class="sm:col-span-2 md:col-span-3 lg:col-span-1 space-y-4 md:space-y-6" data-aos="fade-up" data-aos-duration="600" data-aos-delay="50">
                    <div class="group">
                        <!-- Company Logo & Name -->
                        <div class="flex items-center space-x-3 md:space-x-4 mb-4 md:mb-6">
                            <div class="relative">
                                <div class="w-10 h-10 md:w-12 md:h-12 bg-gradient-to-br from-primary-400 to-primary-600 rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-primary-500/25 transition-all duration-300">
                                    <i class="fas fa-building text-white text-base md:text-lg"></i>
                                </div>
                                <div class="absolute -top-1 -right-1 w-2 h-2 md:w-3 md:h-3 bg-primary-400/30 rounded-full transition-all duration-300 group-hover:scale-150 group-hover:bg-primary-400/60"></div>
                            </div>
                            <h3 class="text-lg md:text-xl font-bold text-white"><?= htmlspecialchars($company['name'] ?? SITE_NAME) ?></h3>
                        </div>

                        <!-- Company Description -->
                        <p class="text-gray-400 text-sm leading-relaxed mb-4 md:mb-6 max-w-sm">
                            <?= htmlspecialchars($company['description'] ?? '') ?>
                        </p>

                        <!-- Social Links with Enhanced Mobile-Friendly Styling -->
                        <div class="flex flex-wrap gap-3 md:gap-4">
                            <?php foreach ($social['links'] ?? [] as $link): ?>
                                <a href="<?= htmlspecialchars($link['url']) ?>"
                                   target="_blank"
                                   rel="noopener noreferrer"
                                   class="social-link <?= $link['color'] ?? 'hover:text-primary-400' ?>"
                                   aria-label="<?= htmlspecialchars($link['label']) ?>">
                                    <i class="<?= htmlspecialchars($link['icon']) ?> text-sm md:text-base"></i>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>

                <!-- Navigation Links -->
                <div class="space-y-4 md:space-y-6" data-aos="fade-up" data-aos-duration="600" data-aos-delay="100">
                    <h3 class="footer-section-title">
                        <i class="fas fa-compass mr-2 text-primary-400"></i>
                        <?= htmlspecialchars($navigation['title'] ?? 'Navigasi') ?>
                    </h3>
                    <ul class="space-y-2 md:space-y-3">
                        <?php foreach ($navigation['links'] ?? [] as $link): ?>
                            <li>
                                <a href="<?= htmlspecialchars($link['url']) ?>" class="footer-link">
                                    <i class="<?= htmlspecialchars($link['icon']) ?> footer-icon"></i>
                                    <?= htmlspecialchars($link['text']) ?>
                                </a>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                </div>

                <!-- Services Links -->
                <div class="space-y-4 md:space-y-6" data-aos="fade-up" data-aos-duration="600" data-aos-delay="150">
                    <h3 class="footer-section-title">
                        <i class="fas fa-cogs mr-2 text-primary-400"></i>
                        <?= htmlspecialchars($services['title'] ?? 'Layanan') ?>
                    </h3>
                    <ul class="space-y-2 md:space-y-3">
                        <?php foreach ($services['links'] ?? [] as $link): ?>
                            <li>
                                <a href="<?= htmlspecialchars($link['url']) ?>" class="footer-link">
                                    <i class="<?= htmlspecialchars($link['icon']) ?> footer-icon"></i>
                                    <?= htmlspecialchars($link['text']) ?>
                                </a>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                </div>

                <!-- Contact Information -->
                <div class="sm:col-span-2 md:col-span-1 space-y-4 md:space-y-6" data-aos="fade-up" data-aos-duration="600" data-aos-delay="200">
                    <h3 class="footer-section-title">
                        <i class="fas fa-phone mr-2 text-primary-400"></i>
                        <?= htmlspecialchars($contact['title'] ?? 'Kontak') ?>
                    </h3>
                    <ul class="space-y-3 md:space-y-4">
                        <?php foreach ($contact['info'] ?? [] as $item): ?>
                            <li class="flex <?= $item['type'] === 'address' ? 'items-start' : 'items-center' ?> group">
                                <div class="w-8 h-8 md:w-9 md:h-9 flex items-center justify-center bg-gray-800/50 rounded-lg mr-3 md:mr-4 flex-shrink-0 group-hover:bg-primary-500/20 transition-all duration-300">
                                    <i class="<?= htmlspecialchars($item['icon']) ?> text-primary-400 text-xs md:text-sm"></i>
                                </div>
                                <div class="text-gray-400 text-sm leading-relaxed">
                                    <?php if ($item['type'] === 'address' && isset($item['lines'])): ?>
                                        <?php foreach ($item['lines'] as $line): ?>
                                            <div class="mb-1"><?= htmlspecialchars($line) ?></div>
                                        <?php endforeach; ?>
                                    <?php elseif (isset($item['url'])): ?>
                                        <a href="<?= htmlspecialchars($item['url']) ?>"
                                           class="hover:text-primary-400 transition-colors duration-300 break-words">
                                            <?= htmlspecialchars($item['text']) ?>
                                        </a>
                                    <?php else: ?>
                                        <span class="break-words"><?= htmlspecialchars($item['text']) ?></span>
                                    <?php endif; ?>
                                </div>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Enhanced Bottom Bar with Better Mobile Layout -->
        <div class="border-t border-gray-800/50 bg-black/50 backdrop-blur-sm" data-aos="fade-up" data-aos-duration="600" data-aos-delay="250">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-4 md:py-6">
                <div class="flex flex-col sm:flex-row justify-between items-center space-y-3 sm:space-y-0 text-center sm:text-left">
                    <p class="text-gray-500 text-xs md:text-sm order-2 sm:order-1">
                        <?= htmlspecialchars($legal['copyright'] ?? '© ' . date('Y') . ' ' . SITE_NAME . '. Hak Cipta Dilindungi.') ?>
                    </p>
                    <div class="flex flex-wrap items-center justify-center sm:justify-end gap-4 md:gap-6 order-1 sm:order-2">
                        <?php foreach ($legal['links'] ?? [] as $link): ?>
                            <a href="<?= htmlspecialchars($link['url']) ?>"
                               class="text-gray-500 hover:text-primary-400 text-xs md:text-sm transition-colors duration-300 whitespace-nowrap">
                                <?= htmlspecialchars($link['text']) ?>
                            </a>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</footer>
